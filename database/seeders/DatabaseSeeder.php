<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->command->info('🌱 Starting database seeding...');

        // Seed in order of dependencies
        $this->call([
            CategorySeeder::class,
            UserSeeder::class,
            AuctionSeeder::class,
            BidSeeder::class,
            PaymentSeeder::class,
        ]);

        $this->command->info('✅ Database seeding completed successfully!');
        $this->command->info('');
        $this->command->info('📊 Summary:');
        $this->command->info('- Categories: ' . \App\Models\Category::count());
        $this->command->info('- Users: ' . \App\Models\User::count());
        $this->command->info('- Auctions: ' . \App\Models\Auction::count());
        $this->command->info('- Bids: ' . \App\Models\Bid::count());
        $this->command->info('- Payments: ' . \App\Models\Payment::count());
        $this->command->info('');
        $this->command->info('🔑 Test Accounts:');
        $this->command->info('- Admin: <EMAIL> (password: password)');
        $this->command->info('- Moderator: <EMAIL> (password: password)');
        $this->command->info('- Premium Seller: <EMAIL> (password: password)');
        $this->command->info('- Test User: <EMAIL> (password: password)');
    }
}
