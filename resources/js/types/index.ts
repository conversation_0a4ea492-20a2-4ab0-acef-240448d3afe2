// User types
export interface User {
  id: number;
  name: string;
  email?: string;
  role: 'user' | 'seller' | 'premium_seller' | 'admin';
  avatar: {
    url?: string;
    thumbnail?: string;
  };
  profile?: {
    bio?: string;
    location?: string;
    phone?: string;
    website?: string;
    social_links?: Record<string, string>;
  };
  verification: {
    is_email_verified: boolean;
    is_phone_verified: boolean;
    is_identity_verified: boolean;
    verification_level: 'unverified' | 'email_verified' | 'phone_verified' | 'fully_verified';
  };
  status: {
    is_active: boolean;
    is_online: boolean;
    last_login_at?: string;
    last_activity_at?: string;
  };
  statistics?: {
    total_auctions: number;
    active_auctions: number;
    total_bids: number;
    won_auctions: number;
    success_rate: number;
    total_spent: number;
    total_earned: number;
    member_since: string;
  };
  metadata: {
    created_at: string;
    updated_at: string;
    is_current_user?: boolean;
    can_contact: boolean;
    can_view_profile: boolean;
  };
}

// Auction types
export interface Auction {
  id: number;
  title: string;
  description: string;
  condition?: 'new' | 'like_new' | 'good' | 'fair' | 'poor';
  status: 'draft' | 'scheduled' | 'active' | 'ended';
  starting_price: MoneyAmount;
  current_bid: MoneyAmount;
  reserve_price?: MoneyAmount & { is_met: boolean };
  buyout_price?: MoneyAmount;
  final_price?: MoneyAmount;
  shipping_cost?: MoneyAmount;
  timing: {
    start_time?: string;
    end_time: string;
    actual_end_time?: string;
    duration_minutes?: number;
    remaining_minutes?: number;
    is_ending_soon: boolean;
  };
  statistics: {
    bids_count: number;
    views_count: number;
    watchers_count: number;
  };
  features: {
    auto_extend: boolean;
    extend_minutes: number;
    is_featured: boolean;
  };
  seller: User;
  category: Category;
  images: AuctionImage[];
  highest_bid?: Bid;
  winner?: User;
  metadata: {
    created_at: string;
    updated_at: string;
    slug: string;
    is_watched?: boolean;
    user_has_bid?: boolean;
    can_bid?: boolean;
    can_edit?: boolean;
  };
}

// Bid types
export interface Bid {
  id: number;
  amount: MoneyAmount;
  max_bid?: MoneyAmount;
  bid_type: 'manual' | 'proxy' | 'auto';
  status: {
    is_winning: boolean;
    is_valid: boolean;
    is_outbid: boolean;
  };
  bidder?: User;
  bidder_name?: string;
  auction?: Auction;
  timing: {
    timestamp: string;
    created_at: string;
    time_ago: string;
  };
  metadata: {
    ip_address?: string;
    user_agent?: string;
    invalidated_at?: string;
    invalidation_reason?: string;
  };
}

// Category types
export interface Category {
  id: number;
  name: string;
  description?: string;
  slug: string;
  parent_id?: number;
  sort_order: number;
  icon?: string;
  image?: string;
  is_active: boolean;
  children?: Category[];
  statistics?: {
    auctions_count: number;
    active_auctions_count: number;
  };
  metadata: {
    created_at: string;
    updated_at: string;
  };
}

// Common types
export interface MoneyAmount {
  amount: number;
  currency: string;
  formatted: string;
}

export interface AuctionImage {
  id: number;
  filename: string;
  url: string;
  thumbnail_url?: string;
  alt_text?: string;
  sort_order: number;
  is_primary: boolean;
  metadata: {
    original_name: string;
    size_bytes: number;
    mime_type: string;
    width: number;
    height: number;
  };
}

// Inertia types
export interface PageProps {
  auth: {
    user: User | null;
  };
  flash: {
    message?: string;
    error?: string;
    success?: string;
  };
  errors: Record<string, string>;
}

// Form types
export interface CreateAuctionForm {
  category_id: number;
  title: string;
  description: string;
  condition?: string;
  starting_price: number;
  reserve_price?: number;
  buyout_price?: number;
  currency?: string;
  start_time?: string;
  end_time: string;
  shipping_cost?: number;
  shipping_options?: string[];
  return_policy?: string;
  terms_conditions?: string;
  auto_extend?: boolean;
  extend_minutes?: number;
}

export interface PlaceBidForm {
  auction_id: number;
  amount: number;
  max_bid?: number;
  currency?: string;
  bid_type?: string;
}

export interface RegisterForm {
  name: string;
  email: string;
  password: string;
  password_confirmation: string;
  phone?: string;
  role?: string;
}

export interface LoginForm {
  email: string;
  password: string;
  remember?: boolean;
}

// Filter and search types
export interface AuctionFilters {
  status?: string;
  category?: number;
  search?: string;
  condition?: string;
  min_price?: number;
  max_price?: number;
  ending_soon?: boolean;
  featured?: boolean;
  sort?: string;
  per_page?: number;
  page?: number;
}

// Payment types
export interface Payment {
  id: number;
  amount: {
    gross_amount: number;
    fee_amount: number;
    net_amount: number;
    currency: string;
    formatted_gross: string;
    formatted_fee: string;
    formatted_net: string;
  };
  refund?: {
    amount: number;
    currency: string;
    formatted: string;
    refunded_at: string;
    remaining_refundable: number;
    formatted_remaining: string;
  };
  status: 'pending' | 'processing' | 'succeeded' | 'failed' | 'cancelled' | 'refunded';
  description: string;
  payment_method: {
    type: 'card' | 'bank_transfer' | 'paypal';
    details: Record<string, any>;
    brand?: string;
    last_four?: string;
  };
  user: User;
  auction: Auction;
  timing: {
    created_at: string;
    processed_at?: string;
    failed_at?: string;
    refunded_at?: string;
    updated_at: string;
  };
  external_references?: {
    payment_intent_id: string;
    stripe_payment_intent_id: string;
    transaction_id: string;
  };
  metadata?: Record<string, any>;
  failure_reason?: string;
}

// Filter types
export interface BidFilters {
  auction_id?: number;
  user_id?: number;
  status?: string;
  per_page?: number;
  page?: number;
}

// WebSocket types
export interface WebSocketMessage {
  channel: string;
  event: string;
  data: any;
}

export interface BidUpdate {
  auction_id: number;
  bidder_id: number;
  bidder_name: string;
  bid_amount: number;
  formatted_amount: string;
  bids_count: number;
  timestamp: string;
}

export interface AuctionStatusUpdate {
  auction_id: number;
  old_status: string;
  new_status: string;
  timestamp: string;
}

export interface NotificationData {
  type: string;
  title: string;
  message: string;
  data: Record<string, any>;
  timestamp: string;
}

// Pagination types
export interface PaginatedData<T> {
  data: T[];
  links: {
    first: string;
    last: string;
    prev: string | null;
    next: string | null;
  };
  meta: {
    current_page: number;
    from: number;
    last_page: number;
    path: string;
    per_page: number;
    to: number;
    total: number;
  };
}
