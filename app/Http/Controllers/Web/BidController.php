<?php

declare(strict_types=1);

namespace App\Http\Controllers\Web;

use App\Actions\Bid\CancelBidAction;
use App\Actions\Bid\PlaceBidAction;
use App\Domain\Auction\Repositories\AuctionRepositoryInterface;
use App\Domain\Auction\Repositories\BidRepositoryInterface;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\User\ValueObjects\UserId;
use App\Http\Controllers\Controller;
use App\Http\Requests\PlaceBidRequest;
use App\Http\Resources\BidResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class BidController extends Controller
{
    public function __construct(
        private BidRepositoryInterface $bidRepository,
        private AuctionRepositoryInterface $auctionRepository
    ) {}

    /**
     * Display bids for an auction
     */
    public function index(int $auctionId, Request $request): Response
    {
        $perPage = min($request->get('per_page', 20), 50);
        $auction = $this->auctionRepository->findByIdOrFail(Id::fromString((string) $auctionId));
        $bids = $this->bidRepository->findByAuction(Id::fromString((string) $auctionId), $perPage);

        return Inertia::render('Bids/Index', [
            'auction' => $auction,
            'bids' => BidResource::collection($bids),
        ]);
    }

    /**
     * Place a bid on an auction
     */
    public function store(PlaceBidRequest $request, PlaceBidAction $action): RedirectResponse
    {
        try {
            $data = $request->validated();
            $data['user_id'] = auth()->id();
            $data['ip_address'] = $request->ip();
            $data['user_agent'] = $request->userAgent();

            $bid = $action->execute($data);

            return redirect()
                ->route('auctions.show', $data['auction_id'])
                ->with('success', 'Bid placed successfully!');
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Cancel a bid
     */
    public function destroy(int $id, CancelBidAction $action): RedirectResponse
    {
        try {
            $userId = UserId::fromString((string) auth()->id());
            $bid = $action->execute($id, $userId);

            return redirect()
                ->back()
                ->with('success', 'Bid cancelled successfully!');
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Get highest bid for an auction (AJAX)
     */
    public function highestBid(int $auctionId): JsonResponse
    {
        try {
            $bid = $this->bidRepository->findHighestByAuction(Id::fromString((string) $auctionId));
            
            return response()->json([
                'data' => $bid ? new BidResource($bid) : null,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to get highest bid',
                'error' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Get recent bids for an auction (AJAX)
     */
    public function recentBids(int $auctionId, Request $request): JsonResponse
    {
        try {
            $minutes = $request->get('minutes', 5);
            $bids = $this->bidRepository->findRecentByAuction(
                Id::fromString((string) $auctionId),
                $minutes
            );

            return response()->json([
                'data' => BidResource::collection($bids),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to get recent bids',
                'error' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Get winning bid for an auction (AJAX)
     */
    public function winningBid(int $auctionId): JsonResponse
    {
        try {
            $bid = $this->bidRepository->findWinningByAuction(Id::fromString((string) $auctionId));
            
            return response()->json([
                'data' => $bid ? new BidResource($bid) : null,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to get winning bid',
                'error' => $e->getMessage(),
            ], 400);
        }
    }
}
